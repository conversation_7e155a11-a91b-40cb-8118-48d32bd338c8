# External Services Integration Guide

## Overview

This document provides comprehensive setup instructions for all external services required by the Ozgaar platform, including API key management, configuration, and backup strategies for production deployment.

## 1. Google Maps Platform Setup

### 1.1 Account Creation & Project Setup

**Prerequisites:**
- Google Cloud Platform account
- Credit card for billing (free tier available)
- Project billing enabled

**Setup Steps:**

1. **Create Google Cloud Project**
   ```bash
   # Using gcloud CLI (optional)
   gcloud projects create ozgaar-maps-prod --name="Ozgaar Maps Production"
   gcloud config set project ozgaar-maps-prod
   ```

2. **Enable Required APIs**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Navigate to "APIs & Services" → "Library"
   - Enable the following APIs:
     - Maps SDK for Android
     - Places API
     - Geocoding API
     - Distance Matrix API
     - Directions API

3. **Create API Credentials**
   - Navigate to "APIs & Services" → "Credentials"
   - Click "Create Credentials" → "API Key"
   - Restrict the API key:
     - Application restrictions: "Android apps"
     - Add package name: `com.ozgaar.android`
     - Add SHA-1 fingerprint from your app
   - API restrictions: Select only the enabled APIs above

### 1.2 API Key Configuration

**Environment-Specific Keys:**

```bash
# Development
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY_DEV=AIzaSyDevelopmentKeyHere123456789

# Staging  
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY_STAGING=AIzaSyStaging Key Here123456789

# Production
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY_PROD=AIzaSyProductionKeyHere123456789
```

**Security Best Practices:**

1. **API Key Restrictions**
   ```json
   {
     "restrictions": {
       "android_key_restriction": {
         "allowed_applications": [{
           "package_name": "com.ozgaar.android",
           "sha1_fingerprint": "YOUR_SHA1_FINGERPRINT"
         }]
       },
       "api_targets": [
         {"service": "maps-android-backend.googleapis.com"},
         {"service": "places-backend.googleapis.com"},
         {"service": "geocoding-backend.googleapis.com"}
       ]
     }
   }
   ```

2. **Usage Monitoring & Alerts**
   - Set up quota monitoring in Google Cloud Console
   - Configure billing alerts at ₹500, ₹1000, ₹2000 thresholds
   - Enable API usage notifications

### 1.3 Implementation Configuration

**React Native Maps Setup:**

```typescript
// src/services/maps/googleMapsConfig.ts
import { Platform } from 'react-native';

export const GOOGLE_MAPS_CONFIG = {
  apiKey: process.env.EXPO_PUBLIC_GOOGLE_MAPS_API_KEY,
  libraries: ['places', 'geometry'] as const,
  region: 'IN', // India-specific results
  language: 'hi', // Hindi as default, will be dynamic based on user preference
  
  // Map styling for Indian context
  mapStyle: [
    {
      featureType: 'poi.business',
      stylers: [{ visibility: 'simplified' }]
    },
    {
      featureType: 'transit.station',
      stylers: [{ visibility: 'on' }] // Show metro/bus stations
    }
  ]
};

export const GEOCODING_CONFIG = {
  componentRestrictions: { country: 'IN' },
  types: ['establishment', 'geocode'],
  fields: ['formatted_address', 'geometry', 'name', 'place_id']
};
```

## 2. SMS Provider Configuration

### 2.1 Primary Provider: Twilio (India)

**Account Setup:**
1. Create account at [twilio.com/try-twilio](https://www.twilio.com/try-twilio)
2. Verify your phone number
3. Purchase Indian phone number for SMS sending
4. Complete regulatory compliance for India (DLT registration)

**DLT (Distributed Ledger Technology) Registration:**
- Required for commercial SMS in India
- Register your business and SMS templates
- Get Entity ID and Template IDs approved
- Process takes 7-10 business days

**Configuration:**

```typescript
// src/services/sms/twilioConfig.ts
export const TWILIO_CONFIG = {
  accountSid: process.env.TWILIO_ACCOUNT_SID,
  authToken: process.env.TWILIO_AUTH_TOKEN,
  fromNumber: process.env.TWILIO_FROM_NUMBER, // +91XXXXXXXXXX
  
  // DLT Configuration for India
  dltConfig: {
    entityId: process.env.TWILIO_DLT_ENTITY_ID,
    templateId: process.env.TWILIO_DLT_TEMPLATE_ID,
    template: 'Your Ozgaar OTP is {#var#}. Valid for 5 minutes. Do not share with anyone.'
  },
  
  // Rate limiting
  rateLimits: {
    perPhone: 3, // 3 OTPs per phone per hour
    perIP: 10,   // 10 OTPs per IP per hour
    cooldown: 60 // 60 seconds between requests
  }
};
```

### 2.2 Backup Provider: MSG91

**Setup Steps:**
1. Register at [msg91.com](https://msg91.com)
2. Complete KYC verification
3. Get approved SMS route for OTP
4. Configure DLT templates

**Configuration:**

```typescript
// src/services/sms/msg91Config.ts
export const MSG91_CONFIG = {
  authKey: process.env.MSG91_AUTH_KEY,
  route: '4', // Transactional route for OTP
  sender: process.env.MSG91_SENDER_ID, // 6-character sender ID
  
  // DLT Configuration
  dltEntityId: process.env.MSG91_DLT_ENTITY_ID,
  dltTemplateId: process.env.MSG91_DLT_TEMPLATE_ID,
  
  // Indian mobile number validation
  mobileValidation: {
    pattern: /^[6-9]\d{9}$/, // Indian mobile numbers
    countryCode: '+91'
  }
};
```

### 2.3 SMS Service Implementation

```typescript
// src/services/sms/smsService.ts
import { TwilioService } from './providers/twilio';
import { MSG91Service } from './providers/msg91';

export class SMSService {
  private primaryProvider = new TwilioService();
  private backupProvider = new MSG91Service();
  
  async sendOTP(phoneNumber: string, otp: string): Promise<SMSResult> {
    try {
      // Try primary provider first
      return await this.primaryProvider.sendOTP(phoneNumber, otp);
    } catch (error) {
      console.warn('Primary SMS provider failed, trying backup:', error);
      
      try {
        return await this.backupProvider.sendOTP(phoneNumber, otp);
      } catch (backupError) {
        console.error('All SMS providers failed:', backupError);
        throw new Error('SMS delivery failed. Please try again.');
      }
    }
  }
  
  async getDeliveryStatus(messageId: string): Promise<DeliveryStatus> {
    // Implementation for checking delivery status
  }
}
```

## 3. Translation Services

### 3.1 Primary: Google Cloud Translation API

**Setup:**
1. Enable Cloud Translation API in Google Cloud Console
2. Create service account with Translation API access
3. Download service account key JSON file

**Configuration:**

```typescript
// src/services/translation/googleTranslate.ts
import { Translate } from '@google-cloud/translate/build/src/v2';

export const TRANSLATION_CONFIG = {
  projectId: process.env.GOOGLE_CLOUD_PROJECT_ID,
  keyFilename: process.env.GOOGLE_CLOUD_KEY_FILE, // Path to service account JSON
  
  // Supported languages for Ozgaar
  supportedLanguages: [
    'hi', // Hindi
    'en', // English
    'ta', // Tamil
    'te', // Telugu
    'bn', // Bengali
    'mr', // Marathi
    'gu', // Gujarati
    'kn'  // Kannada
  ],
  
  // Caching configuration
  cache: {
    enabled: true,
    ttl: 24 * 60 * 60 * 1000, // 24 hours
    maxSize: 1000 // Maximum cached translations
  }
};
```

### 3.2 Backup: Microsoft Translator

```typescript
// src/services/translation/microsoftTranslator.ts
export const MICROSOFT_TRANSLATOR_CONFIG = {
  subscriptionKey: process.env.MICROSOFT_TRANSLATOR_KEY,
  endpoint: 'https://api.cognitive.microsofttranslator.com',
  region: 'southeastasia', // Closest to India
  
  // Language mapping
  languageMapping: {
    'hi': 'hi',
    'ta': 'ta', 
    'te': 'te',
    'bn': 'bn',
    'mr': 'mr',
    'gu': 'gu',
    'kn': 'kn'
  }
};
```

## 4. API Keys Management

### 4.1 Environment Variables

**Development (.env.development):**
```bash
# Google Services
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=AIzaSyDev...
GOOGLE_CLOUD_PROJECT_ID=ozgaar-dev-project

# SMS Providers
TWILIO_ACCOUNT_SID=ACdev...
TWILIO_AUTH_TOKEN=dev_auth_token
TWILIO_FROM_NUMBER=+************
MSG91_AUTH_KEY=dev_msg91_key

# Translation
MICROSOFT_TRANSLATOR_KEY=dev_translator_key

# Supabase
EXPO_PUBLIC_SUPABASE_URL=https://dev.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=dev_anon_key
SUPABASE_SERVICE_ROLE_KEY=dev_service_key
```

**Production (.env.production):**
```bash
# Production keys (stored in CI/CD secrets)
EXPO_PUBLIC_GOOGLE_MAPS_API_KEY=${GOOGLE_MAPS_API_KEY_PROD}
TWILIO_ACCOUNT_SID=${TWILIO_ACCOUNT_SID_PROD}
# ... other production keys
```

### 4.2 Secrets Management

**GitHub Actions Secrets:**
- `GOOGLE_MAPS_API_KEY_PROD`
- `TWILIO_ACCOUNT_SID_PROD`
- `TWILIO_AUTH_TOKEN_PROD`
- `MSG91_AUTH_KEY_PROD`
- `MICROSOFT_TRANSLATOR_KEY_PROD`
- `SUPABASE_PRODUCTION_URL`
- `SUPABASE_PRODUCTION_ANON_KEY`

**Local Development:**
- Use `.env.local` file (gitignored)
- Never commit real API keys to repository
- Use development/testing keys only

## 5. Rate Limiting & Error Handling

### 5.1 API Rate Limits

**Google Maps APIs:**
- Geocoding: 50 requests per second
- Distance Matrix: 100 elements per 10 seconds
- Places: 100 requests per 10 seconds

**SMS Providers:**
- Twilio: 1 request per second per phone number
- MSG91: 100 SMS per minute

**Translation APIs:**
- Google Translate: 100 requests per 10 seconds
- Microsoft Translator: 2M characters per hour

### 5.2 Fallback Strategies

```typescript
// src/services/common/fallbackStrategy.ts
export class FallbackStrategy {
  async executeWithFallback<T>(
    primary: () => Promise<T>,
    backup: () => Promise<T>,
    fallbackToOffline?: () => T
  ): Promise<T> {
    try {
      return await primary();
    } catch (primaryError) {
      console.warn('Primary service failed:', primaryError);
      
      try {
        return await backup();
      } catch (backupError) {
        console.error('Backup service failed:', backupError);
        
        if (fallbackToOffline) {
          console.info('Using offline fallback');
          return fallbackToOffline();
        }
        
        throw new Error('All services unavailable');
      }
    }
  }
}
```

## 6. Monitoring & Alerts

### 6.1 Service Health Monitoring

```typescript
// src/services/monitoring/healthCheck.ts
export class ServiceHealthMonitor {
  async checkGoogleMaps(): Promise<HealthStatus> {
    try {
      // Test geocoding with known address
      const response = await fetch(
        `https://maps.googleapis.com/maps/api/geocode/json?address=Delhi&key=${API_KEY}`
      );
      return response.ok ? 'healthy' : 'degraded';
    } catch {
      return 'unhealthy';
    }
  }
  
  async checkSMSProvider(): Promise<HealthStatus> {
    // Implementation for SMS health check
  }
}
```

### 6.2 Cost Monitoring

**Google Cloud Billing Alerts:**
- ₹100/month - Warning notification
- ₹500/month - Team notification  
- ₹1000/month - Automatic API throttling
- ₹2000/month - Emergency shutdown

**SMS Cost Monitoring:**
- Track per-user OTP costs
- Alert on unusual SMS volume
- Implement fraud detection for bulk requests

This comprehensive external services setup ensures reliable integration with proper fallbacks, monitoring, and cost control for the Ozgaar platform.