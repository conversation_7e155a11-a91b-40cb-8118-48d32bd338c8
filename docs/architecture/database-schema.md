# Database Schema

Based on the data models and PostgreSQL selection via Supabase, here's the concrete database schema for the Ozgaar platform:

## Core Tables Schema

```sql
-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "postgis";

-- Custom types for the Indian job market
CREATE TYPE user_role_enum AS ENUM ('worker', 'poster', 'both');
CREATE TYPE supported_language_enum AS ENUM (
  'hindi', 'english', 'tamil', 'telugu', 'bengali', 
  'marathi', 'gujarati', 'kannada'
);
CREATE TYPE skill_category_enum AS ENUM (
  'electrical', 'plumbing', 'carpentry', 'cooking', 'cleaning',
  'driving', 'delivery', 'security', 'gardening', 'tutoring'
);
CREATE TYPE job_type_enum AS ENUM ('one_time', 'recurring', 'permanent');
CREATE TYPE urgency_enum AS ENUM ('low', 'normal', 'high', 'urgent');
CREATE TYPE job_status_enum AS ENUM ('active', 'paused', 'filled', 'cancelled', 'expired');
CREATE TYPE application_status_enum AS ENUM ('pending', 'accepted', 'rejected', 'withdrawn');
CREATE TYPE review_type_enum AS ENUM ('worker_review', 'poster_review');
CREATE TYPE gender_preference_enum AS ENUM ('any', 'male', 'female');

-- Users table (base entity for all platform users)
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    phone VARCHAR(15) UNIQUE NOT NULL, -- +91 format for India
    email VARCHAR(255),
    full_name VARCHAR(100) NOT NULL,
    preferred_language supported_language_enum DEFAULT 'hindi',
    location GEOGRAPHY(POINT, 4326), -- PostGIS point for lat/lng
    address TEXT,
    user_type user_role_enum DEFAULT 'worker',
    is_verified BOOLEAN DEFAULT FALSE,
    profile_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    last_active_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_phone CHECK (phone ~ '^\+91[6-9][0-9]{9}$'),
    CONSTRAINT valid_email CHECK (email IS NULL OR email ~ '^[^@]+@[^@]+\.[^@]+$')
);

-- Worker personas table (core multi-persona feature)
CREATE TABLE worker_personas (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(100) NOT NULL, -- "Experienced Electrician"
    skill_category skill_category_enum NOT NULL,
    skill_subcategories TEXT[], -- Array of related skills
    description TEXT,
    experience_years INTEGER DEFAULT 0 CHECK (experience_years >= 0),
    
    -- Pricing structure for Indian market
    hourly_rate DECIMAL(8,2) CHECK (hourly_rate >= 0),
    daily_rate DECIMAL(8,2) CHECK (daily_rate >= 0),
    monthly_rate DECIMAL(10,2) CHECK (monthly_rate >= 0),
    is_rate_negotiable BOOLEAN DEFAULT TRUE,
    
    -- Availability and location
    availability_pattern JSONB NOT NULL DEFAULT '{}', -- Weekly schedule
    travel_radius_km INTEGER DEFAULT 10 CHECK (travel_radius_km > 0),
    
    -- Performance metrics
    total_jobs_completed INTEGER DEFAULT 0 CHECK (total_jobs_completed >= 0),
    total_earnings DECIMAL(12,2) DEFAULT 0 CHECK (total_earnings >= 0),
    average_rating DECIMAL(3,2) DEFAULT 0 CHECK (average_rating >= 0 AND average_rating <= 5),
    total_reviews INTEGER DEFAULT 0 CHECK (total_reviews >= 0),
    
    -- Status
    is_active BOOLEAN DEFAULT TRUE,
    is_verified BOOLEAN DEFAULT FALSE,
    verification_date TIMESTAMP WITH TIME ZONE,
    profile_image_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_rating CHECK (
      average_rating = 0 OR (total_reviews > 0 AND average_rating > 0)
    )
);

-- Jobs table (job postings from employers)
CREATE TABLE jobs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    poster_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    description TEXT NOT NULL,
    skill_category skill_category_enum NOT NULL,
    
    -- Location for hyper-local matching
    location GEOGRAPHY(POINT, 4326) NOT NULL,
    address TEXT NOT NULL,
    landmark TEXT,
    
    -- Job specifications
    job_type job_type_enum DEFAULT 'one_time',
    urgency urgency_enum DEFAULT 'normal',
    budget_min DECIMAL(10,2) NOT NULL CHECK (budget_min > 0),
    budget_max DECIMAL(10,2) NOT NULL CHECK (budget_max >= budget_min),
    estimated_duration_hours INTEGER CHECK (estimated_duration_hours > 0),
    
    -- Requirements and preferences
    requirements TEXT,
    preferred_gender gender_preference_enum DEFAULT 'any',
    min_experience_years INTEGER DEFAULT 0 CHECK (min_experience_years >= 0),
    min_rating DECIMAL(3,2) DEFAULT 0 CHECK (min_rating >= 0 AND min_rating <= 5),
    
    -- Status and metrics
    status job_status_enum DEFAULT 'active',
    applications_count INTEGER DEFAULT 0 CHECK (applications_count >= 0),
    views_count INTEGER DEFAULT 0 CHECK (views_count >= 0),
    
    -- Timestamps
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Constraints
    CONSTRAINT valid_expiry CHECK (expires_at > created_at)
);

-- Job applications (connects personas to jobs)
CREATE TABLE job_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    worker_persona_id UUID NOT NULL REFERENCES worker_personas(id) ON DELETE CASCADE,
    
    -- Application details
    status application_status_enum DEFAULT 'pending',
    proposed_rate DECIMAL(8,2),
    message TEXT,
    
    -- Response tracking
    applied_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    responded_at TIMESTAMP WITH TIME ZONE,
    poster_response TEXT,
    
    -- Prevent duplicate applications
    UNIQUE(job_id, worker_persona_id)
);

-- Reviews table (bidirectional trust system)
CREATE TABLE reviews (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    job_id UUID NOT NULL REFERENCES jobs(id) ON DELETE CASCADE,
    reviewer_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    reviewee_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    worker_persona_id UUID REFERENCES worker_personas(id) ON DELETE CASCADE,
    
    -- Review content
    rating INTEGER NOT NULL CHECK (rating >= 1 AND rating <= 5),
    review_text TEXT,
    review_type review_type_enum NOT NULL,
    
    -- Verification
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    -- Prevent duplicate reviews
    UNIQUE(job_id, reviewer_id, review_type),
    
    -- Ensure reviewee consistency
    CONSTRAINT valid_reviewee CHECK (
      (review_type = 'worker_review' AND worker_persona_id IS NOT NULL) OR
      (review_type = 'poster_review' AND worker_persona_id IS NULL)
    )
);

-- Performance optimization indexes
CREATE INDEX idx_users_location ON users USING GIST (location);
CREATE INDEX idx_jobs_location ON jobs USING GIST (location);
CREATE INDEX idx_jobs_active_category ON jobs (skill_category, status, created_at) WHERE status = 'active';
CREATE INDEX idx_personas_active_category ON worker_personas (skill_category, is_active) WHERE is_active = TRUE;
CREATE INDEX idx_applications_job ON job_applications (job_id, status);
CREATE INDEX idx_reviews_reviewee_persona ON reviews (reviewee_id, worker_persona_id, rating);

-- Row Level Security policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE worker_personas ENABLE ROW LEVEL SECURITY;
ALTER TABLE jobs ENABLE ROW LEVEL SECURITY;
ALTER TABLE job_applications ENABLE ROW LEVEL SECURITY;
ALTER TABLE reviews ENABLE ROW LEVEL SECURITY;

-- Basic RLS policies
CREATE POLICY "Users can view own profile" ON users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can manage own personas" ON worker_personas FOR ALL USING (user_id = auth.uid());
CREATE POLICY "Public can view active jobs" ON jobs FOR SELECT USING (status = 'active');

-- Auto-update triggers for ratings and counters
CREATE OR REPLACE FUNCTION update_persona_rating()
RETURNS TRIGGER AS $$
BEGIN
  IF NEW.worker_persona_id IS NOT NULL THEN
    UPDATE worker_personas 
    SET 
      average_rating = (
        SELECT ROUND(AVG(rating)::numeric, 2) 
        FROM reviews 
        WHERE worker_persona_id = NEW.worker_persona_id 
        AND review_type = 'worker_review'
      ),
      total_reviews = (
        SELECT COUNT(*) 
        FROM reviews 
        WHERE worker_persona_id = NEW.worker_persona_id 
        AND review_type = 'worker_review'
      ),
      updated_at = NOW()
    WHERE id = NEW.worker_persona_id;
  END IF;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_persona_rating
  AFTER INSERT OR UPDATE ON reviews
  FOR EACH ROW
  EXECUTE FUNCTION update_persona_rating();
```
