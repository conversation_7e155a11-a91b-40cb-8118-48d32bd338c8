import React, { useState } from "react";
import { View, Text, TextInput, TouchableOpacity, StyleSheet, ActivityIndicator } from "react-native";
import LanguageSelector from "./LanguageSelector";
import { SupportedLanguage } from "../types";
import { Smartphone, Shield, CheckCircle } from "lucide-react-native";

interface AuthFlowProps {
  onAuthComplete: () => void;
}

const AuthFlow = ({ onAuthComplete }: AuthFlowProps) => {
  const [step, setStep] = useState<'phone' | 'otp' | 'profile'>('phone');
  const [phone, setPhone] = useState('');
  const [otp, setOtp] = useState('');
  const [fullName, setFullName] = useState('');
  const [language, setLanguage] = useState<SupportedLanguage>('english');
  const [isLoading, setIsLoading] = useState(false);

  const handlePhoneSubmit = async () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setStep('otp');
    }, 1500);
  };

  const handleOtpSubmit = async () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setStep('profile');
    }, 1500);
  };

  const handleProfileSubmit = async () => {
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      onAuthComplete();
    }, 1500);
  };

  const renderPhoneStep = () => (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <View style={[styles.iconContainer, { backgroundColor: 'rgba(5, 150, 105, 0.1)' }]}>
          <Smartphone size={28} color="#059669" />
        </View>
        <Text style={styles.cardTitle}>Welcome to Ozgaar</Text>
        <Text style={styles.mutedText}>
          Enter your phone number to get started
        </Text>
      </View>
      <View style={styles.cardContent}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Phone Number</Text>
          <View style={styles.phoneInputContainer}>
            <View style={styles.phonePrefix}>
              <Text style={styles.phonePrefixText}>+91</Text>
            </View>
            <TextInput
              style={[styles.input, styles.phoneInput]}
              placeholder="98765 43210"
              value={phone}
              onChangeText={setPhone}
              keyboardType="phone-pad"
              maxLength={10}
            />
          </View>
          <Text style={styles.inputHint}>We'll send a verification code</Text>
        </View>
        <TouchableOpacity 
          style={[styles.button, styles.trustButton, (phone.length !== 10 || isLoading) && styles.buttonDisabled]} 
          onPress={handlePhoneSubmit}
          disabled={phone.length !== 10 || isLoading}
        >
          {isLoading ? <ActivityIndicator color="#fff" /> : <Text style={styles.buttonText}>Send OTP</Text>}
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderOtpStep = () => (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <View style={[styles.iconContainer, { backgroundColor: 'rgba(59, 130, 246, 0.1)' }]}>
          <Shield size={28} color="#3B82F6" />
        </View>
        <Text style={styles.cardTitle}>Verify Your Number</Text>
        <Text style={styles.mutedText}>Enter the 6-digit code sent to +91 {phone}</Text>
      </View>
      <View style={styles.cardContent}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Verification Code</Text>
          <TextInput
            style={[styles.input, styles.otpInput]}
            placeholder="123456"
            value={otp}
            onChangeText={(text) => setOtp(text.replace(/[^0-9]/g, ''))}
            keyboardType="number-pad"
            maxLength={6}
          />
        </View>
        <TouchableOpacity 
          style={[styles.button, styles.trustButton, (otp.length !== 6 || isLoading) && styles.buttonDisabled]} 
          onPress={handleOtpSubmit}
          disabled={otp.length !== 6 || isLoading}
        >
          {isLoading ? <ActivityIndicator color="#fff" /> : <Text style={styles.buttonText}>Verify</Text>}
        </TouchableOpacity>
        <TouchableOpacity 
          style={[styles.button, styles.ghostButton]} 
          onPress={() => setStep('phone')}
        >
          <Text style={styles.ghostButtonText}>Change Number</Text>
        </TouchableOpacity>
      </View>
    </View>
  );

  const renderProfileStep = () => (
    <View style={styles.card}>
      <View style={styles.cardHeader}>
        <View style={[styles.iconContainer, { backgroundColor: 'rgba(34, 197, 94, 0.1)' }]}>
          <CheckCircle size={28} color="#22C55E" />
        </View>
        <Text style={styles.cardTitle}>Complete Your Profile</Text>
        <Text style={styles.mutedText}>Tell us a bit about yourself</Text>
      </View>
      <View style={styles.cardContent}>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Full Name</Text>
          <TextInput
            style={styles.input}
            placeholder="Enter your full name"
            value={fullName}
            onChangeText={setFullName}
          />
        </View>
        <View style={styles.inputGroup}>
          <Text style={styles.label}>Preferred Language</Text>
          <LanguageSelector value={language} onChange={setLanguage} />
        </View>
        <TouchableOpacity 
          style={[styles.button, styles.trustButton, (!fullName.trim() || isLoading) && styles.buttonDisabled]} 
          onPress={handleProfileSubmit}
          disabled={!fullName.trim() || isLoading}
        >
          {isLoading ? <ActivityIndicator color="#fff" /> : <Text style={styles.buttonText}>Get Started</Text>}
        </TouchableOpacity>
      </View>
    </View>
  );

  return (
    <View style={styles.container}>
      {step === 'phone' && renderPhoneStep()}
      {step === 'otp' && renderOtpStep()}
      {step === 'profile' && renderProfileStep()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
  },
  card: {
    width: '100%',
    maxWidth: 400,
    backgroundColor: 'white',
    borderRadius: 12,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    alignItems: 'center',
    padding: 24,
  },
  iconContainer: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#111827',
  },
  mutedText: {
    color: '#6B7280',
    textAlign: 'center',
    marginTop: 4,
  },
  cardContent: {
    padding: 24,
    paddingTop: 0,
    gap: 16,
  },
  inputGroup: {
    gap: 8,
  },
  label: {
    fontWeight: '500',
    color: '#374151',
  },
  input: {
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 10,
    fontSize: 16,
  },
  phoneInputContainer: {
    flexDirection: 'row',
  },
  phonePrefix: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 12,
    backgroundColor: '#F3F4F6',
    borderWidth: 1,
    borderColor: '#D1D5DB',
    borderRightWidth: 0,
    borderTopLeftRadius: 6,
    borderBottomLeftRadius: 6,
  },
  phonePrefixText: {
    fontSize: 16,
    fontWeight: '500',
  },
  phoneInput: {
    flex: 1,
    borderTopLeftRadius: 0,
    borderBottomLeftRadius: 0,
  },
  otpInput: {
    textAlign: 'center',
    fontSize: 18,
    letterSpacing: 8,
  },
  inputHint: {
    fontSize: 12,
    color: '#6B7280',
  },
  button: {
    paddingVertical: 12,
    borderRadius: 6,
    alignItems: 'center',
    justifyContent: 'center',
  },
  trustButton: {
    backgroundColor: '#059669',
  },
  buttonText: {
    color: 'white',
    fontWeight: 'bold',
    fontSize: 16,
  },
  ghostButton: {
    backgroundColor: 'transparent',
  },
  ghostButtonText: {
    color: '#374151',
    fontWeight: '500',
  },
  buttonDisabled: {
    backgroundColor: '#D1D5DB',
  }
});

export default AuthFlow;
