import { MapPin, Clock, IndianRupee, Users } from "lucide-react-native";
import { Job } from "../types";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import TrustBadge from "./TrustBadge";

interface JobCardProps {
  job: Job;
  className?: any;
  onClick?: () => void;
}

const Badge = ({ children, style }: { children: React.ReactNode, style?: any }) => (
  <View style={[styles.badgeBase, style]}>
    <Text style={styles.badgeText}>{children}</Text>
  </View>
);

const JobCard = ({ job, className, onClick }: JobCardProps) => {
  const getUrgencyBadge = (urgency: Job['urgency']) => {
    switch (urgency) {
      case 'urgent':
        return <TrustBadge type="low" text="Urgent" />;
      case 'high':
        return <TrustBadge type="medium" text="High Priority" />;
      case 'medium':
        return <Badge style={{ backgroundColor: '#f1f5f9', borderColor: '#e2e8f0' }}><Text style={{ color: '#334155'}}>Medium Priority</Text></Badge>;
      case 'low':
        return <Badge><Text>Low Priority</Text></Badge>;
      default:
        return null;
    }
  };

  const formatBudget = () => {
    if (job.budget_min && job.budget_max) {
      return `₹${job.budget_min} - ₹${job.budget_max}`;
    } else if (job.budget_min) {
      return `₹${job.budget_min}+`;
    }
    return "Budget negotiable";
  };

  const getSkillCategoryLabel = (category: string) => {
    return category.replace('_', ' ').split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <TouchableOpacity 
      style={[styles.card, className]}
      onPress={onClick}
    >
      <View style={styles.cardHeader}>
        <View style={{ flex: 1 }}>
          <Text style={styles.title} numberOfLines={2}>
            {job.title}
          </Text>
          <Text style={styles.mutedText}>
            {getSkillCategoryLabel(job.skill_category)}
          </Text>
        </View>
        {getUrgencyBadge(job.urgency)}
      </View>
      
      <View style={styles.cardContent}>
        <Text style={styles.mutedText} numberOfLines={2}>
          {job.description}
        </Text>
        
        <View style={styles.skillsContainer}>
          {job.skill_subcategories.slice(0, 3).map((skill, index) => (
            <Badge key={index}>
              {skill}
            </Badge>
          ))}
          {job.skill_subcategories.length > 3 && (
            <Badge>
              +{job.skill_subcategories.length - 3} more
            </Badge>
          )}
        </View>
        
        <View style={{ gap: 8 }}>
          <View style={styles.infoRow}>
            <MapPin size={16} color="#64748b" />
            <Text style={[styles.mutedText, { flex: 1 }]} numberOfLines={1}>{job.location.address}</Text>
          </View>
          
          <View style={{ flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between' }}>
            <View style={styles.infoRow}>
              <IndianRupee size={16} color="#059669" />
              <Text style={{ color: '#059669', fontWeight: '500' }}>{formatBudget()}</Text>
            </View>
            
            <View style={styles.infoRow}>
              <Users size={12} color="#64748b" />
              <Text style={styles.smallMutedText}>{job.applications_count} applied</Text>
            </View>
          </View>
          
          {job.estimated_duration_hours && (
            <View style={styles.infoRow}>
              <Clock size={12} color="#64748b" />
              <Text style={styles.smallMutedText}>Est. {job.estimated_duration_hours}h duration</Text>
            </View>
          )}
        </View>
      </View>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  card: {
    backgroundColor: 'white',
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#e2e8f0',
    borderLeftWidth: 4,
    borderLeftColor: '#059669', // trust-primary
    marginVertical: 8,
  },
  cardHeader: {
    paddingBottom: 12,
    paddingHorizontal: 16,
    paddingTop: 16,
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
    gap: 12,
  },
  title: {
    fontWeight: '600',
    color: '#0f172a',
    fontSize: 16,
  },
  mutedText: {
    fontSize: 14,
    color: '#64748b',
  },
  smallMutedText: {
    fontSize: 12,
    color: '#64748b',
  },
  cardContent: {
    paddingHorizontal: 16,
    paddingBottom: 16,
    gap: 12,
  },
  skillsContainer: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  badgeBase: {
    borderWidth: 1,
    borderColor: '#e2e8f0',
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 9999,
  },
  badgeText: {
    fontSize: 12,
    color: '#334155',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  }
});

export default JobCard;
