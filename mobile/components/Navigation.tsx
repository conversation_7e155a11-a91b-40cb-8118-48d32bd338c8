import React from "react";
import { View, Text, TouchableOpacity, StyleSheet, SafeAreaView } from "react-native";
import { Search, User, Briefcase, MessageCircle, Settings } from "lucide-react-native";

type TabId = 'search' | 'profiles' | 'jobs' | 'messages' | 'settings';

interface NavigationProps {
  activeTab: TabId;
  onTabChange: (tab: TabId) => void;
  className?: any;
}

const Navigation = ({ activeTab, onTabChange, className }: NavigationProps) => {
  const tabs = [
    { id: 'search' as const, icon: Search, label: 'Find Jobs' },
    { id: 'profiles' as const, icon: User, label: 'Personas' },
    { id: 'jobs' as const, icon: Briefcase, label: 'My Jobs' },
    { id: 'messages' as const, icon: MessageCircle, label: 'Messages' },
    { id: 'settings' as const, icon: Settings, label: 'Settings' },
  ];

  return (
    <SafeAreaView style={[styles.safeArea, className]}>
      <View style={styles.nav}>
        {tabs.map(({ id, icon: Icon, label }) => {
          const isActive = activeTab === id;
          return (
            <TouchableOpacity
              key={id}
              style={styles.tab}
              onPress={() => onTabChange(id)}
            >
              <View style={[styles.tabButton, isActive && styles.activeTabButton]}>
                <Icon
                  size={20}
                  color={isActive ? "#FFFFFF" : "#6B7280"}
                />
                <Text style={[styles.tabLabel, isActive && styles.activeTabLabel]}>
                  {label}
                </Text>
              </View>
            </TouchableOpacity>
          );
        })}
      </View>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  safeArea: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'white',
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  nav: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    alignItems: 'center',
    paddingHorizontal: 8,
    paddingVertical: 8,
  },
  tab: {
    flex: 1,
    alignItems: 'center',
    maxWidth: 80,
  },
  tabButton: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 8,
    paddingHorizontal: 4,
    minHeight: 56,
    width: '100%',
    borderRadius: 8,
  },
  activeTabButton: {
    backgroundColor: '#059669', // trust color
  },
  tabLabel: {
    fontSize: 12,
    lineHeight: 16,
    color: '#6B7280', // muted-foreground
    marginTop: 4,
  },
  activeTabLabel: {
    color: '#FFFFFF',
  },
});

export default Navigation;
