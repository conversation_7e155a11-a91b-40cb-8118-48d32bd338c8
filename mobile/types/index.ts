export type SupportedLanguage = 
  | 'hindi' 
  | 'english' 
  | 'tamil' 
  | 'telugu' 
  | 'bengali' 
  | 'marathi' 
  | 'gujarati' 
  | 'kannada';

export interface User {
  id: string;
  phone: string;
  email?: string;
  full_name: string;
  preferred_language: SupportedLanguage;
  location: {
    latitude: number;
    longitude: number;
    address?: string;
  };
  user_type: 'worker' | 'poster' | 'both';
  is_verified: boolean;
  created_at: string;
  last_active_at: string;
}

export type SkillCategory = 
  | 'home_services'
  | 'transport'
  | 'food_services'
  | 'construction'
  | 'beauty_wellness'
  | 'repairs'
  | 'delivery'
  | 'cleaning';

export interface WorkerPersona {
  id: string;
  user_id: string;
  title: string;
  skill_category: SkillCategory;
  skill_subcategories: string[];
  description: string;
  experience_years: number;
  hourly_rate?: number;
  daily_rate?: number;
  is_rate_negotiable: boolean;
  availability_pattern: WeeklyAvailability;
  travel_radius_km: number;
  is_active: boolean;
  total_jobs_completed: number;
  average_rating: number;
  profile_image_url?: string;
  created_at: string;
  updated_at: string;
}

export interface WeeklyAvailability {
  monday: TimeSlot[];
  tuesday: TimeSlot[];
  wednesday: TimeSlot[];
  thursday: TimeSlot[];
  friday: TimeSlot[];
  saturday: TimeSlot[];
  sunday: TimeSlot[];
}

export interface TimeSlot {
  start: string; // HH:MM format
  end: string; // HH:MM format
}

export interface Job {
  id: string;
  title: string;
  description: string;
  skill_category: SkillCategory;
  skill_subcategories: string[];
  location: {
    latitude: number;
    longitude: number;
    address: string;
  };
  budget_min?: number;
  budget_max?: number;
  urgency: 'low' | 'medium' | 'high' | 'urgent';
  preferred_start_time?: string;
  estimated_duration_hours?: number;
  requirements: string[];
  is_active: boolean;
  poster_id: string;
  created_at: string;
  applications_count: number;
}

export interface TrustScore {
  overall_rating: number;
  total_reviews: number;
  identity_verified: boolean;
  phone_verified: boolean;
  email_verified: boolean;
  background_check: boolean;
  skill_certifications: string[];
}
