import React, { useState, useEffect } from "react";
import { View, Text, StyleSheet, ActivityIndicator } from "react-native";
import AsyncStorage from '@react-native-async-storage/async-storage';
import AuthFlow from "../components/AuthFlow";
import Dashboard from "./Dashboard";

const Index = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const authToken = await AsyncStorage.getItem('ozgaar_auth_token');
        setIsAuthenticated(!!authToken);
      } catch (e) {
        // error reading value
      } finally {
        setIsLoading(false);
      }
    };

    checkAuth();
  }, []);

  const handleAuthComplete = async () => {
    try {
      await AsyncStorage.setItem('ozgaar_auth_token', 'sample_token_123');
      setIsAuthenticated(true);
    } catch (e) {
      // saving error
    }
  };

  if (isLoading) {
    return (
      <View style={styles.loaderContainer}>
        <ActivityIndicator size="large" color="#059669" />
        <Text style={styles.loaderText}>Ozgaar</Text>
        <Text style={styles.loaderSubtext}>Trust-first job platform</Text>
      </View>
    );
  }

  if (!isAuthenticated) {
    return <AuthFlow onAuthComplete={handleAuthComplete} />;
  }

  return <Dashboard />;
};

const styles = StyleSheet.create({
  loaderContainer: {
    flex: 1,
    backgroundColor: '#F9FAFB',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loaderText: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#059669',
    marginTop: 16,
  },
  loaderSubtext: {
    color: '#6B7280',
  },
});

export default Index;
