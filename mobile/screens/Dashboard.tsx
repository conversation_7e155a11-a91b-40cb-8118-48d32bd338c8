import React, { useState } from "react";
import { View, Text, StyleSheet, ScrollView, TextInput, TouchableOpacity, SafeAreaView } from "react-native";
import { Bell, MapPin, Filter } from "lucide-react-native";
import { LinearGradient } from 'expo-linear-gradient';

import JobCard from "../components/JobCard";
import PersonaCard from "../components/PersonaCard";
import Navigation from "../components/Navigation";
import TrustBadge from "../components/TrustBadge";
import LanguageSelector from "../components/LanguageSelector";
import { Job, WorkerPersona, SupportedLanguage } from "../types";

// Sample data from original file
const sampleJobs: Job[] = [
    { id: "1", title: "Need Electrician for Home Wiring", description: "Looking for experienced electrician to fix electrical issues in 2BHK apartment.", skill_category: "repairs", skill_subcategories: ["Electrical Work", "Home Wiring", "Troubleshooting"], location: { latitude: 28.6139, longitude: 77.2090, address: "Connaught Place, New Delhi" }, budget_min: 500, budget_max: 1200, urgency: "high", estimated_duration_hours: 3, requirements: ["Tools", "Experience Certificate"], is_active: true, poster_id: "poster1", created_at: "2024-01-15T10:30:00Z", applications_count: 12 },
    { id: "2", title: "Cook Needed for House Party", description: "Need experienced cook for traditional Indian dishes for 20 people house party this weekend.", skill_category: "food_services", skill_subcategories: ["Indian Cuisine", "Party Catering"], location: { latitude: 19.0760, longitude: 72.8777, address: "Bandra West, Mumbai" }, budget_min: 2000, budget_max: 3500, urgency: "urgent", estimated_duration_hours: 6, requirements: ["Own utensils"], is_active: true, poster_id: "poster2", created_at: "2024-01-15T14:15:00Z", applications_count: 8 }
];
const samplePersonas: WorkerPersona[] = [
    { id: "1", user_id: "user1", title: "Rajesh - Master Electrician", skill_category: "repairs", skill_subcategories: ["Home Wiring", "Repairs"], description: "15+ years experienced electrician specializing in residential electrical work.", experience_years: 15, hourly_rate: 200, daily_rate: 1500, is_rate_negotiable: true, availability_pattern: { monday: [], tuesday: [], wednesday: [], thursday: [], friday: [], saturday: [], sunday: [] }, travel_radius_km: 10, is_active: true, total_jobs_completed: 156, average_rating: 4.7, created_at: "2024-01-10T08:00:00Z", updated_at: "2024-01-15T12:00:00Z" }
];

const Dashboard = () => {
    const [activeTab, setActiveTab] = useState<'search' | 'profiles' | 'jobs' | 'messages' | 'settings'>('search');
    const [language, setLanguage] = useState<SupportedLanguage>('english');
    const [searchQuery, setSearchQuery] = useState("");
    const [selectedPersona, setSelectedPersona] = useState<string | null>(null);

    const renderSearchTab = () => (
        <ScrollView style={styles.tabContent}>
            <LinearGradient colors={['#059669', '#3B82F6']} style={styles.header}>
                <View style={styles.headerTopRow}>
                    <View>
                        <Text style={styles.headerTitle}>Find Perfect Jobs</Text>
                        <Text style={styles.headerSubtitle}>Hyper-local opportunities</Text>
                    </View>
                    <View style={{ flexDirection: 'row', alignItems: 'center' }}>
                         <TouchableOpacity style={styles.headerButton}>
                            <Bell size={20} color="white" />
                        </TouchableOpacity>
                    </View>
                </View>
                <TextInput
                    placeholder="Search for jobs..."
                    value={searchQuery}
                    onChangeText={setSearchQuery}
                    style={styles.searchInput}
                    placeholderTextColor="rgba(255,255,255,0.7)"
                />
                <View style={styles.locationRow}>
                    <MapPin size={16} color="white" />
                    <Text style={styles.locationText}>Current location: Delhi NCR</Text>
                    <TouchableOpacity><Text style={styles.changeLocation}>Change</Text></TouchableOpacity>
                </View>
            </LinearGradient>

            <View style={[styles.card, styles.trustCard]}>
                <View style={{flex: 1}}>
                    <Text style={styles.cardTitle}>Your Trust Profile</Text>
                    <View style={styles.trustBadges}>
                        <TrustBadge type="verified" text="Phone Verified" />
                        <TrustBadge type="pending" text="ID Pending" />
                    </View>
                </View>
                <TouchableOpacity><Text style={styles.linkText}>View Details</Text></TouchableOpacity>
            </View>

            <View style={styles.filtersContainer}>
                <TouchableOpacity style={styles.filterButton}><Filter size={16} /><Text>Filters</Text></TouchableOpacity>
                <TouchableOpacity style={styles.filterButton}><Text>Urgent Jobs</Text></TouchableOpacity>
                <TouchableOpacity style={styles.filterButton}><Text>Near Me</Text></TouchableOpacity>
            </View>

            <View style={styles.listContainer}>
                <Text style={styles.listTitle}>Recommended Jobs</Text>
                {sampleJobs.map(job => <JobCard key={job.id} job={job} />)}
            </View>
        </ScrollView>
    );
    
    const renderGenericTab = (title: string, subtitle: string, children: React.ReactNode) => (
        <ScrollView style={styles.tabContent}>
            <View style={styles.pageHeader}>
                <Text style={styles.pageTitle}>{title}</Text>
                <Text style={styles.pageSubtitle}>{subtitle}</Text>
            </View>
            <View style={styles.listContainer}>{children}</View>
        </ScrollView>
    );

    const renderProfilesTab = () => renderGenericTab("Your Worker Personas", "Manage your professional profiles", (
        <>
            <TouchableOpacity style={styles.primaryButton}><Text style={styles.primaryButtonText}>Create New Persona</Text></TouchableOpacity>
            {samplePersonas.map(p => <PersonaCard key={p.id} persona={p} isActive={selectedPersona === p.id} onClick={() => setSelectedPersona(p.id)} />)}
        </>
    ));

    const renderJobsTab = () => renderGenericTab("My Applications", "Track your job applications", (
        <View style={styles.card}><Text style={styles.placeholderText}>No applications yet.</Text></View>
    ));

    const renderMessagesTab = () => renderGenericTab("Messages", "Communicate with job posters", (
        <View style={styles.card}><Text style={styles.placeholderText}>No messages yet.</Text></View>
    ));

    const renderSettingsTab = () => renderGenericTab("Settings", "Manage your account preferences", (
        <View style={styles.card}>
            <Text style={styles.cardTitle}>Language Preferences</Text>
            <LanguageSelector value={language} onChange={setLanguage} />
        </View>
    ));

    const renderContent = () => {
        switch (activeTab) {
            case 'search': return renderSearchTab();
            case 'profiles': return renderProfilesTab();
            case 'jobs': return renderJobsTab();
            case 'messages': return renderMessagesTab();
            case 'settings': return renderSettingsTab();
        }
    };

    return (
        <SafeAreaView style={styles.container}>
            <View style={{flex: 1, paddingBottom: 60}}>
                {renderContent()}
            </View>
            <Navigation activeTab={activeTab} onTabChange={setActiveTab} />
        </SafeAreaView>
    );
};

const styles = StyleSheet.create({
    container: { flex: 1, backgroundColor: '#F3F4F6' },
    tabContent: { flex: 1 },
    header: { padding: 24, borderBottomLeftRadius: 24, borderBottomRightRadius: 24 },
    headerTopRow: { flexDirection: 'row', justifyContent: 'space-between', alignItems: 'center', marginBottom: 16 },
    headerTitle: { fontSize: 24, fontWeight: 'bold', color: 'white' },
    headerSubtitle: { color: 'rgba(255,255,255,0.9)' },
    headerButton: { padding: 8 },
    searchInput: { backgroundColor: 'rgba(255,255,255,0.2)', color: 'white', borderRadius: 8, padding: 12, marginBottom: 12 },
    locationRow: { flexDirection: 'row', alignItems: 'center', gap: 8 },
    locationText: { color: 'rgba(255,255,255,0.9)', fontSize: 14 },
    changeLocation: { color: 'white', fontWeight: 'bold' },
    card: { backgroundColor: 'white', borderRadius: 8, padding: 16, marginHorizontal: 16 },
    trustCard: { flexDirection: 'row', alignItems: 'center', justifyContent: 'space-between', marginTop: 16 },
    cardTitle: { fontWeight: '600', marginBottom: 8 },
    trustBadges: { flexDirection: 'row', gap: 8, flexWrap: 'wrap' },
    linkText: { color: '#059669', fontWeight: '500' },
    filtersContainer: { flexDirection: 'row', gap: 8, padding: 16 },
    filterButton: { flexDirection: 'row', alignItems: 'center', gap: 4, backgroundColor: 'white', paddingHorizontal: 12, paddingVertical: 8, borderRadius: 999 },
    listContainer: { paddingHorizontal: 16, gap: 16, paddingBottom: 16 },
    listTitle: { fontSize: 18, fontWeight: '600' },
    pageHeader: { backgroundColor: 'white', padding: 24, borderBottomWidth: 1, borderBottomColor: '#E5E7EB' },
    pageTitle: { fontSize: 24, fontWeight: 'bold' },
    pageSubtitle: { color: '#6B7280', marginTop: 4 },
    primaryButton: { backgroundColor: '#059669', padding: 12, borderRadius: 8, alignItems: 'center' },
    primaryButtonText: { color: 'white', fontWeight: 'bold' },
    placeholderText: { color: '#6B7280', textAlign: 'center' },
});

export default Dashboard;
