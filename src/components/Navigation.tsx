import { Search, User, Briefcase, MessageCircle, Settings } from "lucide-react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface NavigationProps {
  activeTab: 'search' | 'profiles' | 'jobs' | 'messages' | 'settings';
  onTabChange: (tab: 'search' | 'profiles' | 'jobs' | 'messages' | 'settings') => void;
  className?: string;
}

const Navigation = ({ activeTab, onTabChange, className }: NavigationProps) => {
  const tabs = [
    { id: 'search' as const, icon: Search, label: 'Find Jobs' },
    { id: 'profiles' as const, icon: User, label: 'Personas' },
    { id: 'jobs' as const, icon: Briefcase, label: 'My Jobs' },
    { id: 'messages' as const, icon: MessageCircle, label: 'Messages' },
    { id: 'settings' as const, icon: Settings, label: 'Settings' },
  ];

  return (
    <nav className={cn(
      "fixed bottom-0 left-0 right-0 bg-card border-t border-border z-50",
      "flex items-center justify-around px-2 py-2 safe-area-pb",
      className
    )}>
      {tabs.map(({ id, icon: Icon, label }) => (
        <Button
          key={id}
          variant={activeTab === id ? "trust" : "ghost"}
          size="sm"
          onClick={() => onTabChange(id)}
          className={cn(
            "flex-1 flex-col h-auto py-2 px-1 min-h-[56px]",
            "text-xs gap-1 max-w-[80px]"
          )}
        >
          <Icon className={cn(
            "w-5 h-5",
            activeTab === id ? "text-trust-primary-foreground" : "text-muted-foreground"
          )} />
          <span className={cn(
            "leading-tight",
            activeTab === id ? "text-trust-primary-foreground" : "text-muted-foreground"
          )}>
            {label}
          </span>
        </Button>
      ))}
    </nav>
  );
};

export default Navigation;