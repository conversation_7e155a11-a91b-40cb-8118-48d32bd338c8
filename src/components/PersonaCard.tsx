import { Star, MapP<PERSON>, Clock, IndianRupee } from "lucide-react";
import { WorkerPersona } from "@/types";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import TrustBadge from "./TrustBadge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { cn } from "@/lib/utils";

interface PersonaCardProps {
  persona: WorkerPersona;
  className?: string;
  onClick?: () => void;
  isActive?: boolean;
}

const PersonaCard = ({ persona, className, onClick, isActive }: PersonaCardProps) => {
  const getSkillCategoryLabel = (category: string) => {
    return category.replace('_', ' ').split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  const formatRate = () => {
    if (persona.hourly_rate && persona.daily_rate) {
      return `₹${persona.hourly_rate}/hr • ₹${persona.daily_rate}/day`;
    } else if (persona.hourly_rate) {
      return `₹${persona.hourly_rate}/hour`;
    } else if (persona.daily_rate) {
      return `₹${persona.daily_rate}/day`;
    }
    return "Rate negotiable";
  };

  const getTrustLevel = (rating: number) => {
    if (rating >= 4.5) return 'high';
    if (rating >= 3.5) return 'medium';
    return 'low';
  };

  return (
    <Card 
      className={cn(
        "job-card cursor-pointer transition-all duration-200",
        isActive && "ring-2 ring-trust-primary border-trust-primary",
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start gap-3">
          <Avatar className="w-12 h-12">
            <AvatarImage src={persona.profile_image_url} alt={persona.title} />
            <AvatarFallback className="bg-trust-primary text-trust-primary-foreground font-semibold">
              {persona.title.split(' ').map(n => n[0]).join('').toUpperCase()}
            </AvatarFallback>
          </Avatar>
          
          <div className="flex-1">
            <h3 className="font-semibold text-foreground line-clamp-1">
              {persona.title}
            </h3>
            <p className="text-sm text-muted-foreground">
              {getSkillCategoryLabel(persona.skill_category)}
            </p>
            <div className="flex items-center gap-2 mt-1">
              <div className="flex items-center gap-1">
                <Star className="w-3 h-3 fill-warning text-warning" />
                <span className="text-xs font-medium">{persona.average_rating.toFixed(1)}</span>
              </div>
              <TrustBadge 
                type={getTrustLevel(persona.average_rating)} 
                text={`${persona.total_jobs_completed} jobs`} 
              />
            </div>
          </div>
          
          {!persona.is_active && (
            <Badge variant="outline" className="text-xs">Inactive</Badge>
          )}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <p className="text-sm text-muted-foreground line-clamp-2">
          {persona.description}
        </p>
        
        <div className="flex flex-wrap gap-1">
          {persona.skill_subcategories.slice(0, 3).map((skill, index) => (
            <Badge key={index} variant="secondary" className="text-xs">
              {skill}
            </Badge>
          ))}
          {persona.skill_subcategories.length > 3 && (
            <Badge variant="secondary" className="text-xs">
              +{persona.skill_subcategories.length - 3}
            </Badge>
          )}
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <MapPin className="w-4 h-4" />
            <span>Within {persona.travel_radius_km}km radius</span>
          </div>
          
          <div className="flex items-center gap-2 text-sm font-medium text-trust-primary">
            <IndianRupee className="w-4 h-4" />
            <span>{formatRate()}</span>
            {persona.is_rate_negotiable && (
              <Badge variant="outline" className="text-xs">Negotiable</Badge>
            )}
          </div>
          
          <div className="flex items-center gap-2 text-xs text-muted-foreground">
            <Clock className="w-3 h-3" />
            <span>{persona.experience_years} years experience</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default PersonaCard;