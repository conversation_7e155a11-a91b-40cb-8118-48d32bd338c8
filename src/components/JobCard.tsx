import { MapPin, Clock, IndianRupee, Users } from "lucide-react";
import { Job } from "@/types";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import TrustBadge from "./TrustBadge";
import { cn } from "@/lib/utils";

interface JobCardProps {
  job: Job;
  className?: string;
  onClick?: () => void;
}

const JobCard = ({ job, className, onClick }: JobCardProps) => {
  const getUrgencyBadge = (urgency: Job['urgency']) => {
    switch (urgency) {
      case 'urgent':
        return <TrustBadge type="low" text="Urgent" />;
      case 'high':
        return <TrustBadge type="medium" text="High Priority" />;
      case 'medium':
        return <Badge variant="secondary">Medium Priority</Badge>;
      case 'low':
        return <Badge variant="outline">Low Priority</Badge>;
      default:
        return null;
    }
  };

  const formatBudget = () => {
    if (job.budget_min && job.budget_max) {
      return `₹${job.budget_min} - ₹${job.budget_max}`;
    } else if (job.budget_min) {
      return `₹${job.budget_min}+`;
    }
    return "Budget negotiable";
  };

  const getSkillCategoryLabel = (category: string) => {
    return category.replace('_', ' ').split(' ').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ');
  };

  return (
    <Card 
      className={cn(
        "job-card cursor-pointer border-l-4 border-l-trust-primary",
        className
      )}
      onClick={onClick}
    >
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between gap-3">
          <div className="flex-1">
            <h3 className="font-semibold text-foreground line-clamp-2">
              {job.title}
            </h3>
            <p className="text-sm text-muted-foreground mt-1">
              {getSkillCategoryLabel(job.skill_category)}
            </p>
          </div>
          {getUrgencyBadge(job.urgency)}
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        <p className="text-sm text-muted-foreground line-clamp-2">
          {job.description}
        </p>
        
        <div className="flex flex-wrap gap-2">
          {job.skill_subcategories.slice(0, 3).map((skill, index) => (
            <Badge key={index} variant="outline" className="text-xs">
              {skill}
            </Badge>
          ))}
          {job.skill_subcategories.length > 3 && (
            <Badge variant="outline" className="text-xs">
              +{job.skill_subcategories.length - 3} more
            </Badge>
          )}
        </div>
        
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <MapPin className="w-4 h-4" />
            <span className="line-clamp-1">{job.location.address}</span>
          </div>
          
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2 text-sm font-medium text-trust-primary">
              <IndianRupee className="w-4 h-4" />
              <span>{formatBudget()}</span>
            </div>
            
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Users className="w-3 h-3" />
              <span>{job.applications_count} applied</span>
            </div>
          </div>
          
          {job.estimated_duration_hours && (
            <div className="flex items-center gap-2 text-xs text-muted-foreground">
              <Clock className="w-3 h-3" />
              <span>Est. {job.estimated_duration_hours}h duration</span>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default JobCard;