import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import LanguageSelector from "./LanguageSelector";
import { SupportedLanguage } from "@/types";
import { Smartphone, Shield, CheckCircle } from "lucide-react";

interface AuthFlowProps {
  onAuthComplete: () => void;
}

const AuthFlow = ({ onAuthComplete }: AuthFlowProps) => {
  const [step, setStep] = useState<'phone' | 'otp' | 'profile'>('phone');
  const [phone, setPhone] = useState('');
  const [otp, setOtp] = useState('');
  const [fullName, setFullName] = useState('');
  const [language, setLanguage] = useState<SupportedLanguage>('english');
  const [isLoading, setIsLoading] = useState(false);

  const handlePhoneSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setStep('otp');
    }, 1500);
  };

  const handleOtpSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      setStep('profile');
    }, 1500);
  };

  const handleProfileSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      setIsLoading(false);
      onAuthComplete();
    }, 1500);
  };

  const renderPhoneStep = () => (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-trust-primary/10 rounded-full flex items-center justify-center mb-4">
          <Smartphone className="w-6 h-6 text-trust-primary" />
        </div>
        <CardTitle className="text-2xl">Welcome to Ozgaar</CardTitle>
        <p className="text-muted-foreground">
          Enter your phone number to get started with trust-first job matching
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handlePhoneSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <div className="flex">
              <div className="flex items-center px-3 bg-muted border border-r-0 rounded-l-md">
                <span className="text-sm font-medium">+91</span>
              </div>
              <Input
                id="phone"
                type="tel"
                placeholder="98765 43210"
                value={phone}
                onChange={(e) => setPhone(e.target.value)}
                className="rounded-l-none"
                maxLength={10}
                required
              />
            </div>
            <p className="text-xs text-muted-foreground">
              We'll send a verification code to this number
            </p>
          </div>
          
          <Button 
            type="submit" 
            variant="trust" 
            className="w-full" 
            disabled={phone.length !== 10 || isLoading}
          >
            {isLoading ? "Sending..." : "Send OTP"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );

  const renderOtpStep = () => (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-information/10 rounded-full flex items-center justify-center mb-4">
          <Shield className="w-6 h-6 text-information" />
        </div>
        <CardTitle className="text-2xl">Verify Your Number</CardTitle>
        <p className="text-muted-foreground">
          Enter the 6-digit code sent to +91 {phone}
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleOtpSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="otp">Verification Code</Label>
            <Input
              id="otp"
              type="text"
              placeholder="123456"
              value={otp}
              onChange={(e) => setOtp(e.target.value.replace(/\D/g, ''))}
              maxLength={6}
              className="text-center text-lg tracking-widest"
              required
            />
          </div>
          
          <Button 
            type="submit" 
            variant="trust" 
            className="w-full" 
            disabled={otp.length !== 6 || isLoading}
          >
            {isLoading ? "Verifying..." : "Verify"}
          </Button>
          
          <Button 
            type="button" 
            variant="ghost" 
            className="w-full" 
            onClick={() => setStep('phone')}
          >
            Change Number
          </Button>
        </form>
      </CardContent>
    </Card>
  );

  const renderProfileStep = () => (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto w-12 h-12 bg-success/10 rounded-full flex items-center justify-center mb-4">
          <CheckCircle className="w-6 h-6 text-success" />
        </div>
        <CardTitle className="text-2xl">Complete Your Profile</CardTitle>
        <p className="text-muted-foreground">
          Tell us a bit about yourself to get started
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleProfileSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="name">Full Name</Label>
            <Input
              id="name"
              type="text"
              placeholder="Enter your full name"
              value={fullName}
              onChange={(e) => setFullName(e.target.value)}
              required
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="language">Preferred Language</Label>
            <LanguageSelector 
              value={language} 
              onChange={setLanguage}
              className="w-full"
            />
          </div>
          
          <Button 
            type="submit" 
            variant="trust" 
            className="w-full" 
            disabled={!fullName.trim() || isLoading}
          >
            {isLoading ? "Creating Profile..." : "Get Started"}
          </Button>
        </form>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gradient-to-br from-trust-primary/5 to-information/5 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        {step === 'phone' && renderPhoneStep()}
        {step === 'otp' && renderOtpStep()}
        {step === 'profile' && renderProfileStep()}
      </div>
    </div>
  );
};

export default AuthFlow;