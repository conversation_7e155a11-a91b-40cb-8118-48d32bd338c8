import { Shield, CheckCircle, Clock, AlertTriangle } from "lucide-react";
import { cn } from "@/lib/utils";

interface TrustBadgeProps {
  type: 'verified' | 'pending' | 'high' | 'medium' | 'low';
  text: string;
  className?: string;
}

const TrustBadge = ({ type, text, className }: TrustBadgeProps) => {
  const getIcon = () => {
    switch (type) {
      case 'verified':
        return <CheckCircle className="w-3 h-3" />;
      case 'pending':
        return <Clock className="w-3 h-3" />;
      case 'high':
        return <Shield className="w-3 h-3" />;
      case 'medium':
        return <Shield className="w-3 h-3" />;
      case 'low':
        return <AlertTriangle className="w-3 h-3" />;
      default:
        return <CheckCircle className="w-3 h-3" />;
    }
  };

  const getVariantClasses = () => {
    switch (type) {
      case 'verified':
        return 'trust-badge verified';
      case 'pending':
        return 'trust-badge pending';
      case 'high':
        return 'bg-success/10 text-success border border-success/20';
      case 'medium':
        return 'bg-warning/10 text-warning border border-warning/20';
      case 'low':
        return 'bg-error/10 text-error border border-error/20';
      default:
        return 'trust-badge verified';
    }
  };

  return (
    <span className={cn(
      "inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full",
      getVariantClasses(),
      className
    )}>
      {getIcon()}
      {text}
    </span>
  );
};

export default TrustBadge;