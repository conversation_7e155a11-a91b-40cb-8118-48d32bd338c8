@import url('https://fonts.googleapis.com/css2?family=Noto+Sans:wght@400;500;600;700&family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

/* Ozgaar Design System - Trust-first job platform for India */

@layer base {
  :root {
    /* Core Brand Colors */
    --background: 0 0% 100%;
    --foreground: 220 13% 18%;

    /* Ozgaar Brand Palette */
    --trust-primary: 146 60% 35%; /* Sea Green #2E8B57 */
    --trust-primary-foreground: 0 0% 100%;
    
    --urgency: 33 100% 50%; /* Dark Orange #FF8C00 */
    --urgency-foreground: 0 0% 100%;
    
    --information: 225 73% 57%; /* Royal Blue #4169E1 */
    --information-foreground: 0 0% 100%;
    
    --success: 120 61% 34%; /* Forest Green #228B22 */
    --success-foreground: 0 0% 100%;
    
    --warning: 51 100% 50%; /* Gold #FFD700 */
    --warning-foreground: 220 13% 18%;
    
    --error: 348 83% 47%; /* Crimson #DC143C */
    --error-foreground: 0 0% 100%;

    /* UI System Colors */
    --primary: 146 60% 35%; /* Trust Primary */
    --primary-foreground: 0 0% 100%;

    --secondary: 220 14% 96%;
    --secondary-foreground: 220 13% 18%;

    --muted: 220 14% 96%;
    --muted-foreground: 220 9% 46%;

    --accent: 225 73% 97%;
    --accent-foreground: 225 73% 57%;

    --destructive: 348 83% 47%;
    --destructive-foreground: 0 0% 100%;

    --border: 220 13% 91%;
    --input: 220 13% 91%;
    --ring: 146 60% 35%;

    --card: 0 0% 100%;
    --card-foreground: 220 13% 18%;

    --popover: 0 0% 100%;
    --popover-foreground: 220 13% 18%;

    --radius: 0.75rem;

    /* Trust Indicators */
    --verified-badge: 146 60% 35%;
    --verification-pending: 51 100% 50%;
    --trust-high: 120 61% 34%;
    --trust-medium: 51 100% 50%;
    --trust-low: 348 83% 47%;

    /* Gradients */
    --gradient-trust: linear-gradient(135deg, hsl(146 60% 35%), hsl(146 70% 45%));
    --gradient-hero: linear-gradient(135deg, hsl(146 60% 35%), hsl(225 73% 57%));
    --gradient-success: linear-gradient(135deg, hsl(120 61% 34%), hsl(146 60% 35%));

    /* Shadows */
    --shadow-trust: 0 10px 30px -10px hsl(146 60% 35% / 0.3);
    --shadow-card: 0 4px 20px -4px hsl(220 13% 18% / 0.1);
    --shadow-elevated: 0 8px 30px -8px hsl(220 13% 18% / 0.15);

    /* Animations */
    --transition-smooth: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    --transition-spring: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 240 5.3% 26.1%;

    --sidebar-primary: 240 5.9% 10%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 240 4.8% 95.9%;

    --sidebar-accent-foreground: 240 5.9% 10%;

    --sidebar-border: 220 13% 91%;

    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;

    /* Dark mode Ozgaar colors */
    --trust-primary: 146 50% 45%;
    --urgency: 33 100% 60%;
    --information: 225 73% 67%;
    --success: 120 61% 44%;
    --warning: 51 100% 60%;
    --error: 348 83% 57%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-noto;
  }

  /* Mobile-first responsive typography */
  h1 { @apply text-2xl md:text-3xl lg:text-4xl font-bold; }
  h2 { @apply text-xl md:text-2xl lg:text-3xl font-semibold; }
  h3 { @apply text-lg md:text-xl lg:text-2xl font-semibold; }
}

@layer components {
  /* Trust Badge Styles */
  .trust-badge {
    @apply inline-flex items-center gap-1 px-2 py-1 text-xs font-medium rounded-full;
  }
  
  .trust-badge.verified {
    @apply bg-trust-primary/10 text-trust-primary border border-trust-primary/20;
  }
  
  .trust-badge.pending {
    @apply bg-warning/10 text-warning border border-warning/20;
  }

  /* Job Card Animations */
  .job-card {
    transition: var(--transition-smooth);
  }
  
  .job-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-elevated);
  }

  /* Mobile-optimized touch targets */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
}