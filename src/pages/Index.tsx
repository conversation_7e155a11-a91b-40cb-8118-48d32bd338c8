import { useState, useEffect } from "react";
import AuthFlow from "@/components/AuthFlow";
import Dashboard from "./Dashboard";

const Index = () => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Simulate checking for existing auth session
    const checkAuth = () => {
      setTimeout(() => {
        // Check localStorage or session storage for auth token
        const authToken = localStorage.getItem('ozgaar_auth_token');
        setIsAuthenticated(!!authToken);
        setIsLoading(false);
      }, 1000);
    };

    checkAuth();
  }, []);

  const handleAuthComplete = () => {
    // Simulate storing auth token
    localStorage.setItem('ozgaar_auth_token', 'sample_token_123');
    setIsAuthenticated(true);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-trust-primary/5 to-information/5 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 bg-trust-primary rounded-full flex items-center justify-center mb-4 mx-auto animate-pulse">
            <span className="text-2xl font-bold text-white">O</span>
          </div>
          <h1 className="text-2xl font-bold text-trust-primary mb-2">Ozgaar</h1>
          <p className="text-muted-foreground">Trust-first job platform</p>
        </div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return <AuthFlow onAuthComplete={handleAuthComplete} />;
  }

  return <Dashboard />;
};

export default Index;
