import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON>, Filter } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import JobCard from "@/components/JobCard";
import PersonaCard from "@/components/PersonaCard";
import Navigation from "@/components/Navigation";
import TrustBadge from "@/components/TrustBadge";
import LanguageSelector from "@/components/LanguageSelector";
import { Job, WorkerPersona, SupportedLanguage } from "@/types";

// Sample data for demonstration
const sampleJobs: Job[] = [
  {
    id: "1",
    title: "Need Electrician for Home Wiring",
    description: "Looking for experienced electrician to fix electrical issues in 2BHK apartment. Must have proper tools and certification.",
    skill_category: "repairs",
    skill_subcategories: ["Electrical Work", "Home Wiring", "Troubleshooting"],
    location: {
      latitude: 28.6139,
      longitude: 77.2090,
      address: "Connaught Place, New Delhi"
    },
    budget_min: 500,
    budget_max: 1200,
    urgency: "high",
    estimated_duration_hours: 3,
    requirements: ["Tools", "Experience Certificate"],
    is_active: true,
    poster_id: "poster1",
    created_at: "2024-01-15T10:30:00Z",
    applications_count: 12
  },
  {
    id: "2",
    title: "Cook Needed for House Party",
    description: "Need experienced cook for traditional Indian dishes for 20 people house party this weekend.",
    skill_category: "food_services",
    skill_subcategories: ["Indian Cuisine", "Party Catering", "Traditional Cooking"],
    location: {
      latitude: 19.0760,
      longitude: 72.8777,
      address: "Bandra West, Mumbai"
    },
    budget_min: 2000,
    budget_max: 3500,
    urgency: "urgent",
    estimated_duration_hours: 6,
    requirements: ["Own utensils", "Menu planning"],
    is_active: true,
    poster_id: "poster2",
    created_at: "2024-01-15T14:15:00Z",
    applications_count: 8
  }
];

const samplePersonas: WorkerPersona[] = [
  {
    id: "1",
    user_id: "user1",
    title: "Rajesh - Master Electrician",
    skill_category: "repairs",
    skill_subcategories: ["Home Wiring", "Electrical Repairs", "Motor Installation"],
    description: "15+ years experienced electrician specializing in residential electrical work. Licensed and insured.",
    experience_years: 15,
    hourly_rate: 200,
    daily_rate: 1500,
    is_rate_negotiable: true,
    availability_pattern: {
      monday: [{ start: "09:00", end: "18:00" }],
      tuesday: [{ start: "09:00", end: "18:00" }],
      wednesday: [{ start: "09:00", end: "18:00" }],
      thursday: [{ start: "09:00", end: "18:00" }],
      friday: [{ start: "09:00", end: "18:00" }],
      saturday: [{ start: "10:00", end: "16:00" }],
      sunday: []
    },
    travel_radius_km: 10,
    is_active: true,
    total_jobs_completed: 156,
    average_rating: 4.7,
    created_at: "2024-01-10T08:00:00Z",
    updated_at: "2024-01-15T12:00:00Z"
  }
];

const Dashboard = () => {
  const [activeTab, setActiveTab] = useState<'search' | 'profiles' | 'jobs' | 'messages' | 'settings'>('search');
  const [language, setLanguage] = useState<SupportedLanguage>('english');
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPersona, setSelectedPersona] = useState<string | null>(null);

  const renderSearchTab = () => (
    <div className="space-y-4">
      {/* Header */}
      <div className="bg-gradient-to-r from-trust-primary to-information text-white p-6 rounded-b-3xl">
        <div className="flex items-center justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold">Find Perfect Jobs</h1>
            <p className="text-white/90">Hyper-local opportunities near you</p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="ghost" size="icon" className="text-white hover:bg-white/20">
              <Bell className="w-5 h-5" />
            </Button>
            <LanguageSelector 
              value={language} 
              onChange={setLanguage}
              className="w-32 bg-white/20 border-white/30 text-white"
            />
          </div>
        </div>
        
        {/* Search and location */}
        <div className="space-y-3">
          <div className="relative">
            <Input
              placeholder="Search for jobs..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="bg-white/20 border-white/30 text-white placeholder:text-white/70"
            />
          </div>
          
          <div className="flex items-center gap-2 text-white/90">
            <MapPin className="w-4 h-4" />
            <span className="text-sm">Current location: Delhi NCR</span>
            <Button variant="ghost" size="sm" className="text-white/90 hover:bg-white/20 p-1 h-auto">
              Change
            </Button>
          </div>
        </div>
      </div>

      {/* Trust summary */}
      <Card className="mx-4">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold">Your Trust Profile</h3>
              <div className="flex items-center gap-2 mt-1">
                <TrustBadge type="verified" text="Phone Verified" />
                <TrustBadge type="pending" text="ID Pending" />
              </div>
            </div>
            <Button variant="outline" size="sm">View Details</Button>
          </div>
        </CardContent>
      </Card>

      {/* Quick filters */}
      <div className="px-4">
        <div className="flex items-center gap-2 mb-4">
          <Button variant="outline" size="sm">
            <Filter className="w-4 h-4" />
            Filters
          </Button>
          <Button variant="outline" size="sm">Urgent Jobs</Button>
          <Button variant="outline" size="sm">Near Me</Button>
        </div>
      </div>

      {/* Job listings */}
      <div className="px-4 space-y-4">
        <h2 className="text-lg font-semibold">Recommended Jobs</h2>
        {sampleJobs.map((job) => (
          <JobCard 
            key={job.id} 
            job={job}
            onClick={() => console.log('Job clicked:', job.id)}
          />
        ))}
      </div>
    </div>
  );

  const renderProfilesTab = () => (
    <div className="space-y-4">
      <div className="bg-card p-6 border-b">
        <h1 className="text-2xl font-bold mb-2">Your Worker Personas</h1>
        <p className="text-muted-foreground">Manage your professional profiles</p>
      </div>

      <div className="px-4 space-y-4">
        <Button variant="trust" className="w-full">
          Create New Persona
        </Button>

        {samplePersonas.map((persona) => (
          <PersonaCard 
            key={persona.id} 
            persona={persona}
            isActive={selectedPersona === persona.id}
            onClick={() => setSelectedPersona(persona.id)}
          />
        ))}
      </div>
    </div>
  );

  const renderJobsTab = () => (
    <div className="space-y-4">
      <div className="bg-card p-6 border-b">
        <h1 className="text-2xl font-bold mb-2">My Applications</h1>
        <p className="text-muted-foreground">Track your job applications</p>
      </div>

      <div className="px-4">
        <Card>
          <CardHeader>
            <CardTitle>No applications yet</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground mb-4">
              Start applying to jobs to see your applications here.
            </p>
            <Button variant="trust" onClick={() => setActiveTab('search')}>
              Browse Jobs
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderMessagesTab = () => (
    <div className="space-y-4">
      <div className="bg-card p-6 border-b">
        <h1 className="text-2xl font-bold mb-2">Messages</h1>
        <p className="text-muted-foreground">Communicate with job posters</p>
      </div>

      <div className="px-4">
        <Card>
          <CardContent className="p-8 text-center">
            <p className="text-muted-foreground">
              No messages yet. Your conversations will appear here.
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderSettingsTab = () => (
    <div className="space-y-4">
      <div className="bg-card p-6 border-b">
        <h1 className="text-2xl font-bold mb-2">Settings</h1>
        <p className="text-muted-foreground">Manage your account preferences</p>
      </div>

      <div className="px-4 space-y-4">
        <Card>
          <CardHeader>
            <CardTitle>Language Preferences</CardTitle>
          </CardHeader>
          <CardContent>
            <LanguageSelector 
              value={language} 
              onChange={setLanguage}
              className="w-full"
            />
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Notification Settings</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <span>Job Recommendations</span>
                <Button variant="outline" size="sm">On</Button>
              </div>
              <div className="flex items-center justify-between">
                <span>New Messages</span>
                <Button variant="outline" size="sm">On</Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );

  const renderActiveTab = () => {
    switch (activeTab) {
      case 'search':
        return renderSearchTab();
      case 'profiles':
        return renderProfilesTab();
      case 'jobs':
        return renderJobsTab();
      case 'messages':
        return renderMessagesTab();
      case 'settings':
        return renderSettingsTab();
      default:
        return renderSearchTab();
    }
  };

  return (
    <div className="min-h-screen bg-background pb-20">
      {renderActiveTab()}
      <Navigation 
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />
    </div>
  );
};

export default Dashboard;